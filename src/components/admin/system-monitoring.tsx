'use client';

import { useState, useEffect, useCallback } from 'react';
import {
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	CardH<PERSON>er,
	Card<PERSON><PERSON>le,
	<PERSON>ading<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import {
	Activity,
	RefreshCw,
	Trash2,
	Database,
	Zap,
	Brain,
	AlertTriangle,
	CheckCircle,
	Clock,
} from 'lucide-react';

interface SystemHealth {
	status: string;
	details: Record<string, any>;
}

interface TokenUsageStats {
	totalTokens: number;
	totalCost: number;
	requestCount: number;
	averageTokensPerRequest: number;
	averageCostPerRequest: number;
}

interface SystemData {
	health: SystemHealth;
	tokenUsage: TokenUsageStats;
	recentErrors: any[];
}

export function SystemMonitoring() {
	const [systemData, setSystemData] = useState<SystemData | null>(null);
	const [loading, setLoading] = useState(true);
	const [refreshing, setRefreshing] = useState(false);
	const [clearingCache, setClearingCache] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [retryCount, setRetryCount] = useState(0);
	const { showSuccess, showError } = useToast();

	const fetchSystemData = useCallback(
		async (isRefresh = false, retryAttempt = 0) => {
			if (isRefresh) {
				setRefreshing(true);
			} else {
				setLoading(true);
			}

			// Clear previous error when starting fresh
			if (retryAttempt === 0) {
				setError(null);
			}

			try {
				const response = await fetch('/api/admin/system', {
					credentials: 'include',
					headers: {
						'Content-Type': 'application/json',
					},
				});

				if (!response.ok) {
					throw new Error(`HTTP ${response.status}: ${response.statusText}`);
				}

				const data = await response.json();

				if (data.success) {
					setSystemData(data.data);
					setError(null);
					setRetryCount(0);
				} else {
					throw new Error(data.error || 'Failed to fetch system data');
				}
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : 'Unknown error';
				setError(errorMessage);

				// Retry logic - max 3 attempts with exponential backoff
				if (retryAttempt < 2) {
					const delay = Math.pow(2, retryAttempt) * 1000; // 1s, 2s, 4s
					setTimeout(() => {
						setRetryCount(retryAttempt + 1);
						fetchSystemData(isRefresh, retryAttempt + 1);
					}, delay);
				} else {
					showError(
						new Error(
							`Failed to load system data after ${
								retryAttempt + 1
							} attempts: ${errorMessage}`
						)
					);
				}
			} finally {
				if (retryAttempt === 0 || retryAttempt >= 2) {
					setLoading(false);
					setRefreshing(false);
				}
			}
		},
		[showError]
	);

	useEffect(() => {
		fetchSystemData();
	}, [fetchSystemData]);

	const handleRefresh = () => {
		fetchSystemData(true);
	};

	const clearCache = async () => {
		setClearingCache(true);

		try {
			const response = await fetch('/api/admin/system', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({}),
			});

			const data = await response.json();

			if (response.ok && data.success) {
				showSuccess(data.message || 'Cache cleared successfully');
				// Refresh system data after clearing cache
				fetchSystemData(true);
			} else {
				throw new Error(data.error || 'Failed to clear cache');
			}
		} catch (error) {
			showError(new Error('Failed to clear cache'));
		} finally {
			setClearingCache(false);
		}
	};

	const getHealthStatusColor = (status: string) => {
		switch (status.toLowerCase()) {
			case 'healthy':
				return 'text-green-600 bg-green-100 dark:bg-green-900';
			case 'warning':
				return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900';
			case 'error':
				return 'text-red-600 bg-red-100 dark:bg-red-900';
			default:
				return 'text-gray-600 bg-gray-100 dark:bg-gray-900';
		}
	};

	const getHealthIcon = (status: string) => {
		switch (status.toLowerCase()) {
			case 'healthy':
				return <CheckCircle className="h-5 w-5 text-green-600" />;
			case 'warning':
				return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
			case 'error':
				return <AlertTriangle className="h-5 w-5 text-red-600" />;
			default:
				return <Activity className="h-5 w-5 text-gray-600" />;
		}
	};

	if (loading) {
		return (
			<div className="flex flex-col items-center justify-center h-64 space-y-4">
				<LoadingSpinner className="h-8 w-8" />
				<div className="text-center">
					<p className="text-gray-600 dark:text-gray-400">Loading system data...</p>
					{retryCount > 0 && (
						<p className="text-sm text-gray-500 dark:text-gray-500 mt-1">
							Retry attempt: {retryCount}/3
						</p>
					)}
				</div>
			</div>
		);
	}

	if (!systemData) {
		return (
			<div className="text-center py-12">
				<AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
				<h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
					Failed to load system data
				</h3>
				{error && (
					<p className="text-sm text-red-600 dark:text-red-400 mb-4 max-w-md mx-auto">
						{error}
					</p>
				)}
				{retryCount > 0 && (
					<p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
						Retry attempt: {retryCount}/3
					</p>
				)}
				<div className="flex justify-center space-x-2">
					<Button
						onClick={() => fetchSystemData()}
						disabled={refreshing}
						variant="outline"
					>
						{refreshing ? (
							<LoadingSpinner className="h-4 w-4 mr-2" />
						) : (
							<RefreshCw className="h-4 w-4 mr-2" />
						)}
						Try Again
					</Button>
					<Button onClick={() => window.location.reload()} variant="outline">
						Reload Page
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 dark:text-white">
						System Monitoring
					</h1>
					<p className="text-gray-600 dark:text-gray-400 mt-1">
						Monitor system health and performance
					</p>
				</div>
				<div className="flex space-x-2">
					<Button onClick={clearCache} disabled={clearingCache} variant="outline">
						{clearingCache ? (
							<LoadingSpinner className="h-4 w-4 mr-2" />
						) : (
							<Trash2 className="h-4 w-4 mr-2" />
						)}
						Clear Cache
					</Button>
					<Button onClick={handleRefresh} disabled={refreshing} variant="outline">
						<RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
						Refresh
					</Button>
				</div>
			</div>

			{/* System Health Overview */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<Activity className="h-5 w-5 mr-2" />
						System Health
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="flex items-center space-x-4 mb-6">
						{getHealthIcon(systemData.health.status)}
						<Badge className={getHealthStatusColor(systemData.health.status)}>
							{systemData.health.status.toUpperCase()}
						</Badge>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
						<div className="flex items-center space-x-3">
							<Database className="h-8 w-8 text-blue-600" />
							<div>
								<div className="font-medium">Database</div>
								<div className="text-sm text-gray-600 dark:text-gray-400">
									{systemData.health.details.database}
								</div>
							</div>
						</div>

						<div className="flex items-center space-x-3">
							<Zap className="h-8 w-8 text-yellow-600" />
							<div>
								<div className="font-medium">Cache</div>
								<div className="text-sm text-gray-600 dark:text-gray-400">
									{systemData.health.details.cache}
								</div>
							</div>
						</div>

						<div className="flex items-center space-x-3">
							<Brain className="h-8 w-8 text-purple-600" />
							<div>
								<div className="font-medium">AI Services</div>
								<div className="text-sm text-gray-600 dark:text-gray-400">
									{systemData.health.details.ai_services}
								</div>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Token Usage Stats */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<Brain className="h-5 w-5 mr-2" />
						AI Token Usage
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
						<div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
							<div className="text-2xl font-bold text-blue-600">
								{systemData.tokenUsage.totalTokens?.toLocaleString() || 0}
							</div>
							<div className="text-sm text-gray-600 dark:text-gray-400">
								Total Tokens
							</div>
						</div>

						<div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
							<div className="text-2xl font-bold text-green-600">
								${systemData.tokenUsage.totalCost?.toFixed(2) || '0.00'}
							</div>
							<div className="text-sm text-gray-600 dark:text-gray-400">
								Total Cost
							</div>
						</div>

						<div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
							<div className="text-2xl font-bold text-purple-600">
								{systemData.tokenUsage.requestCount?.toLocaleString() || 0}
							</div>
							<div className="text-sm text-gray-600 dark:text-gray-400">
								API Requests
							</div>
						</div>

						<div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
							<div className="text-2xl font-bold text-orange-600">
								{systemData.tokenUsage.averageTokensPerRequest?.toFixed(0) || 0}
							</div>
							<div className="text-sm text-gray-600 dark:text-gray-400">
								Avg Tokens/Request
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Recent Errors */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<AlertTriangle className="h-5 w-5 mr-2" />
						Recent Errors
					</CardTitle>
				</CardHeader>
				<CardContent>
					{systemData.recentErrors.length === 0 ? (
						<div className="text-center py-8">
							<CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
							<p className="text-gray-600 dark:text-gray-400">
								No recent errors found
							</p>
						</div>
					) : (
						<div className="space-y-4">
							{systemData.recentErrors.map((error, index) => (
								<div
									key={index}
									className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
								>
									<div className="flex items-start space-x-3">
										<AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
										<div className="flex-1">
											<div className="font-medium text-red-800 dark:text-red-200">
												{error.message || 'Unknown error'}
											</div>
											<div className="text-sm text-red-600 dark:text-red-400 mt-1">
												{error.timestamp && (
													<span className="flex items-center">
														<Clock className="h-3 w-3 mr-1" />
														{new Date(error.timestamp).toLocaleString()}
													</span>
												)}
											</div>
										</div>
									</div>
								</div>
							))}
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
