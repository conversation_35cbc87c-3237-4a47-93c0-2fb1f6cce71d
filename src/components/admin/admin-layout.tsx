'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { <PERSON>ton, LoadingSpinner } from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import {
	Users,
	MessageSquare,
	Shield,
	LogOut,
	Menu,
	X,
	Home,
	Activity,
	FileText,
} from 'lucide-react';

interface AdminLayoutProps {
	children: React.ReactNode;
}

const adminNavItems = [
	{
		href: '/admin',
		label: 'Dashboard',
		icon: Home,
		description: 'Overview and statistics',
	},
	{
		href: '/admin/users',
		label: 'Users',
		icon: Users,
		description: 'Manage user accounts',
	},
	{
		href: '/admin/feedback',
		label: 'Feedback',
		icon: MessageSquare,
		description: 'User feedback management',
	},
	{
		href: '/admin/system',
		label: 'System',
		icon: Activity,
		description: 'System monitoring',
	},
	{
		href: '/admin/audit',
		label: 'Audit Logs',
		icon: FileText,
		description: 'Security audit trail',
	},
];

export function AdminLayout({ children }: AdminLayoutProps) {
	const [sidebarOpen, setSidebarOpen] = useState(false);
	const [loggingOut, setLoggingOut] = useState(false);
	const router = useRouter();
	const pathname = usePathname();
	const { showSuccess, showError } = useToast();

	const handleLogout = async () => {
		setLoggingOut(true);
		try {
			const response = await fetch('/api/admin/auth', {
				method: 'DELETE',
			});

			if (response.ok) {
				showSuccess('Logged out successfully');
				router.push('/admin/login');
			} else {
				showError(new Error('Logout failed'));
			}
		} catch (error) {
			showError(new Error('Logout failed'));
		} finally {
			setLoggingOut(false);
		}
	};

	return (
		<div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
			{/* Mobile sidebar overlay */}
			{sidebarOpen && (
				<div
					className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
					onClick={() => setSidebarOpen(false)}
				/>
			)}

			{/* Sidebar */}
			<div
				className={`fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0 flex flex-col ${
					sidebarOpen ? 'translate-x-0' : '-translate-x-full'
				}`}
			>
				<div className="flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-gray-700">
					<div className="flex items-center space-x-2">
						<Shield className="h-8 w-8 text-blue-600" />
						<span className="text-xl font-bold text-gray-900 dark:text-white">
							Admin Panel
						</span>
					</div>
					<Button
						variant="ghost"
						size="sm"
						className="lg:hidden"
						onClick={() => setSidebarOpen(false)}
					>
						<X className="h-5 w-5" />
					</Button>
				</div>

				<nav className="mt-6 px-3 flex-1 overflow-y-auto">
					<div className="space-y-1">
						{adminNavItems.map((item) => {
							const Icon = item.icon;
							const isActive = pathname === item.href;

							return (
								<Button
									key={item.href}
									variant={isActive ? 'default' : 'ghost'}
									className={`w-full justify-start text-left ${
										isActive
											? 'bg-blue-600 text-white hover:bg-blue-700'
											: 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
									}`}
									onClick={() => {
										router.push(item.href);
										setSidebarOpen(false);
									}}
								>
									<Icon className="h-5 w-5 mr-3" />
									<div className="flex flex-col items-start">
										<span className="font-medium">{item.label}</span>
										<span className="text-xs opacity-75">
											{item.description}
										</span>
									</div>
								</Button>
							);
						})}
					</div>
				</nav>

				{/* Logout button - Fixed at bottom */}
				<div className="p-3 border-t border-gray-200 dark:border-gray-700">
					<Button
						variant="outline"
						className="w-full justify-start text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
						onClick={handleLogout}
						disabled={loggingOut}
					>
						{loggingOut ? (
							<LoadingSpinner className="h-5 w-5 mr-3" />
						) : (
							<LogOut className="h-5 w-5 mr-3" />
						)}
						{loggingOut ? 'Logging out...' : 'Logout'}
					</Button>
				</div>
			</div>

			{/* Main content */}
			<div className="flex-1 flex flex-col">
				{/* Top bar */}
				<div className="sticky top-0 z-30 flex h-16 items-center justify-between bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6">
					<Button
						variant="ghost"
						size="sm"
						className="lg:hidden"
						onClick={() => setSidebarOpen(true)}
						data-testid="mobile-menu-button"
					>
						<Menu className="h-5 w-5" />
					</Button>

					<div className="flex items-center space-x-4">
						<div className="text-sm text-gray-600 dark:text-gray-400">
							Admin Dashboard
						</div>
					</div>
				</div>

				{/* Page content */}
				<main className="flex-1 p-6">{children}</main>
			</div>
		</div>
	);
}
