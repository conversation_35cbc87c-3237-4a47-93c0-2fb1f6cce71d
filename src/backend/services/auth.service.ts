import { Provider, User } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { UserService } from './user.service';
import { AuditService } from './audit.service';

export interface AuthService {
	providerLogin(
		provider: Provider,
		provider_id: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<User>;
	usernamePasswordLogin(
		username: string,
		password: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<User>;
	getUserById(userId: string): Promise<User | null>;
	findOrCreateUserByProvider(
		provider: Provider,
		providerId: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<User>;
	logout(userId: string, ipAddress?: string, userAgent?: string): Promise<void>;
}

export class AuthServiceImpl implements AuthService {
	constructor(
		private readonly getUserService: () => UserService,
		private readonly getAuditService: () => AuditService
	) {}

	async providerLogin(
		provider: Provider,
		provider_id: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<User> {
		let user = await this.getUserService().getUserByProviderId(provider, provider_id);
		let isNewUser = false;

		if (!user) {
			user = await this.getUserService().createUser({
				provider,
				provider_id,
			});
			isNewUser = true;
		}

		// Log audit event
		try {
			const auditService = this.getAuditService();
			await auditService.logEvent({
				action: isNewUser ? 'REGISTER' : 'LOGIN',
				resource: 'user',
				resource_id: user.id,
				user_id: user.id,
				details: {
					provider,
					provider_id,
					isNewUser,
				},
				ip_address: ipAddress,
				user_agent: userAgent,
			});
		} catch (error) {
			console.error('Failed to log audit event for provider login:', error);
		}

		return user;
	}

	async usernamePasswordLogin(
		username: string,
		password: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<User> {
		let user = await this.getUserService().getUserByUsername(username);
		let isNewUser = false;

		if (!user) {
			// Tạo user mới nếu chưa tồn tại
			const hashedPassword = await bcrypt.hash(password, 10);
			user = await this.getUserService().createUserWithPassword({
				username,
				password_hash: hashedPassword,
				provider: Provider.USERNAME_PASSWORD,
				provider_id: username, // Sử dụng username làm provider_id
			});
			isNewUser = true;
		} else {
			// Kiểm tra mật khẩu nếu user đã tồn tại
			if (!user.password_hash) {
				throw new Error('User exists but has no password set');
			}

			const isValidPassword = await bcrypt.compare(password, user.password_hash);
			if (!isValidPassword) {
				// Log failed login attempt
				try {
					const auditService = this.getAuditService();
					await auditService.logEvent({
						action: 'LOGIN_FAILED',
						resource: 'user',
						resource_id: user.id,
						user_id: user.id,
						details: {
							username,
							reason: 'Invalid password',
						},
						ip_address: ipAddress,
						user_agent: userAgent,
					});
				} catch (error) {
					console.error('Failed to log audit event for failed login:', error);
				}
				throw new Error('Invalid password');
			}
		}

		// Log successful login/registration
		try {
			const auditService = this.getAuditService();
			await auditService.logEvent({
				action: isNewUser ? 'REGISTER' : 'LOGIN',
				resource: 'user',
				resource_id: user.id,
				user_id: user.id,
				details: {
					username,
					provider: Provider.USERNAME_PASSWORD,
					isNewUser,
				},
				ip_address: ipAddress,
				user_agent: userAgent,
			});
		} catch (error) {
			console.error('Failed to log audit event for username/password login:', error);
		}

		return user;
	}

	async getUserById(userId: string): Promise<User | null> {
		return this.getUserService().getUserById(userId);
	}

	async findOrCreateUserByProvider(
		provider: Provider,
		providerId: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<User> {
		let user = await this.getUserService().getUserByProviderId(provider, providerId);
		let isNewUser = false;

		if (!user) {
			user = await this.getUserService().createUser({
				provider,
				provider_id: providerId,
			});
			isNewUser = true;
		}

		// Log audit event
		try {
			const auditService = this.getAuditService();
			await auditService.logEvent({
				action: isNewUser ? 'REGISTER' : 'ACCESS',
				resource: 'user',
				resource_id: user.id,
				user_id: user.id,
				details: {
					provider,
					provider_id: providerId,
					isNewUser,
				},
				ip_address: ipAddress,
				user_agent: userAgent,
			});
		} catch (error) {
			console.error('Failed to log audit event for findOrCreateUserByProvider:', error);
		}

		return user;
	}

	async logout(userId: string, ipAddress?: string, userAgent?: string): Promise<void> {
		try {
			const auditService = this.getAuditService();
			await auditService.logEvent({
				action: 'LOGOUT',
				resource: 'user',
				resource_id: userId,
				user_id: userId,
				details: {},
				ip_address: ipAddress,
				user_agent: userAgent,
			});
		} catch (error) {
			console.error('Failed to log audit event for logout:', error);
		}
	}
}
