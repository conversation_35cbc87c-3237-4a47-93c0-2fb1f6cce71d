import { jwtVerify, type JWTPayload } from 'jose';
import { NextRequest, NextResponse } from 'next/server';
import { getUserService } from '@/backend/wire';
import { Role } from '@prisma/client';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/api-error-middleware';

/**
 * Verifies a JWT token and checks admin role in middleware context
 */
async function verifyAdminTokenInMiddleware(token: string, secret: string): Promise<JWTPayload> {
	try {
		const { payload } = await jwtVerify<JWTPayload>(token, new TextEncoder().encode(secret));
		return payload;
	} catch {
		throw new Error('Invalid token');
	}
}

/**
 * Admin authentication middleware that verifies both authentication and admin role
 */
export async function adminMiddleware(request: NextRequest) {
	const jwtCookieName = process.env.JWT_COOKIE_NAME;
	const jwtSecret = process.env.JWT_SECRET;

	if (!jwtCookieName?.length || !jwtSecret?.length) {
		console.error('JWT_COOKIE_NAME or JWT_SECRET is not defined in environment variables');
		return Response.json({ error: 'Internal server error' }, { status: 500 });
	}

	// Extract token from cookie
	const token = request.cookies.get(jwtCookieName)?.value;

	if (!token) {
		return Response.json({ error: 'Unauthorized - No token provided' }, { status: 401 });
	}

	try {
		// Verify and decode token
		const payload = await verifyAdminTokenInMiddleware(token, jwtSecret);
		const userId = payload.sub;

		if (!userId) {
			return Response.json(
				{ error: 'Unauthorized - Invalid user ID in token' },
				{ status: 401 }
			);
		}

		// Check if user exists and has admin role
		const userService = getUserService();
		const user = await userService.getUserById(userId);

		if (!user) {
			return Response.json({ error: 'Unauthorized - User not found' }, { status: 401 });
		}

		if (user.role !== Role.ADMIN) {
			return Response.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
		}

		if (user.disabled) {
			return Response.json({ error: 'Forbidden - Account disabled' }, { status: 403 });
		}

		// Add user context to request headers
		request.headers.set('x-user-id', userId);
		request.headers.set('x-user-role', user.role);

		return request;
	} catch (error) {
		console.error('Admin token verification failed:', error);
		return Response.json({ error: 'Unauthorized - Invalid token' }, { status: 401 });
	}
}

/**
 * Helper function to check if a user has admin role (for use in API routes)
 */
export async function requireAdminRole(userId: string): Promise<boolean> {
	try {
		const userService = getUserService();
		const user = await userService.getUserById(userId);

		return user?.role === Role.ADMIN && !user.disabled;
	} catch (error) {
		console.error('Error checking admin role:', error);
		return false;
	}
}

/**
 * Middleware wrapper for API routes that require admin access
 */
export function withAdminAuth(handler: ApiHandler): ApiHandler {
	return async (request: NextRequest, context?: any): Promise<NextResponse> => {
		const authResult = await adminMiddleware(request);

		// If authResult is a Response (error), return it
		if (authResult instanceof Response) {
			// Convert Response to NextResponse to match return type
			return new NextResponse(authResult.body, {
				status: authResult.status,
				statusText: authResult.statusText,
				headers: authResult.headers,
			});
		}

		// Otherwise, authResult is the modified request, proceed with handler
		const result = await handler(authResult, context);

		// Ensure we return a NextResponse
		if (result instanceof NextResponse) {
			return result;
		}

		// This shouldn't happen with ApiHandler, but just in case
		return result as NextResponse;
	};
}
